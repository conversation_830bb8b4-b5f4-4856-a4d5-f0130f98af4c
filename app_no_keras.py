"""
Application Streamlit Hugging Face sans Keras
Utilise uniquement l'API d'inférence pour éviter les conflits
"""

import streamlit as st
import requests
import os
from dotenv import load_dotenv
from PIL import Image
import io

# Charger les variables d'environnement
load_dotenv()

# Configuration de la page
st.set_page_config(
    page_title="🤗 Hugging Face Explorer - Sans Keras",
    page_icon="🤗",
    layout="wide",
    initial_sidebar_state="expanded"
)

def call_huggingface_api(model_name, inputs, token=None, task_type="text"):
    """Appel générique à l'API d'inférence Hugging Face"""
    try:
        url = f"https://api-inference.huggingface.co/models/{model_name}"
        headers = {"Content-Type": "application/json"}
        
        if token:
            headers["Authorization"] = f"Bearer {token}"
        
        # Adapter le payload selon le type de tâche
        if task_type == "question-answering":
            payload = inputs  # inputs est déjà un dict avec question et context
        else:
            payload = {"inputs": inputs}
        
        timeout = 90 if task_type == "image" else 30
        response = requests.post(url, headers=headers, json=payload, timeout=timeout)
        
        if response.status_code == 200:
            if task_type == "image":
                return {"success": True, "data": response.content, "type": "image"}
            else:
                return {"success": True, "data": response.json(), "type": "json"}
        elif response.status_code == 503:
            return {"success": False, "error": "Modèle en cours de chargement"}
        elif response.status_code == 401:
            return {"success": False, "error": "Token invalide ou manquant"}
        elif response.status_code == 404:
            return {"success": False, "error": "Modèle non trouvé"}
        else:
            return {"success": False, "error": f"Erreur API {response.status_code}"}
    
    except requests.exceptions.Timeout:
        return {"success": False, "error": "Timeout - Requête trop longue"}
    except Exception as e:
        return {"success": False, "error": f"Erreur: {str(e)}"}

# Interface principale
st.title("🤗 Hugging Face Explorer - Version Sans Keras")
st.markdown("Application optimisée utilisant uniquement l'API d'inférence")

# Sidebar
st.sidebar.header("⚙️ Configuration")

# Token
default_token = os.getenv("HUGGINGFACE_API_TOKEN", "")
hf_token = st.sidebar.text_input(
    "Token Hugging Face",
    type="password",
    value=default_token,
    help="Chargé automatiquement depuis .env"
)

if hf_token:
    st.sidebar.success(f"✅ Token configuré ({hf_token[:10]}...)")
else:
    st.sidebar.warning("⚠️ Aucun token configuré")

# Test de connexion
if st.sidebar.button("🔄 Tester la connexion"):
    with st.spinner("Test..."):
        result = call_huggingface_api("gpt2", "Hello", hf_token)
        if result["success"]:
            st.sidebar.success("✅ Connexion OK")
        else:
            st.sidebar.error(f"❌ {result['error']}")

# Interface principale avec onglets
tab1, tab2, tab3, tab4 = st.tabs(["📝 Texte", "🎨 Images", "❓ Q&A", "📊 Info"])

with tab1:
    st.header("📝 Traitement de Texte")
    
    # Sous-onglets pour différents types
    subtab1, subtab2, subtab3 = st.tabs(["😊 Sentiment", "✨ Génération", "📄 Résumé"])
    
    with subtab1:
        st.subheader("Analyse de Sentiment")
        text = st.text_area("Texte à analyser", "I love this application!", height=100)
        model = "cardiffnlp/twitter-roberta-base-sentiment-latest"
        
        if st.button("Analyser le sentiment"):
            with st.spinner("Analyse..."):
                result = call_huggingface_api(model, text, hf_token)
                if result["success"]:
                    st.success("✅ Résultat:")
                    data = result["data"]
                    if isinstance(data, list):
                        for item in data:
                            label = item.get("label", "Unknown")
                            score = item.get("score", 0)
                            st.write(f"**{label}**: {score:.4f}")
                    else:
                        st.json(data)
                else:
                    st.error(f"❌ {result['error']}")
    
    with subtab2:
        st.subheader("Génération de Texte")
        prompt = st.text_area("Prompt", "Once upon a time", height=100)
        model = st.selectbox("Modèle", ["gpt2", "microsoft/DialoGPT-medium"])
        
        if st.button("Générer du texte"):
            with st.spinner("Génération..."):
                result = call_huggingface_api(model, prompt, hf_token)
                if result["success"]:
                    st.success("✅ Texte généré:")
                    data = result["data"]
                    if isinstance(data, list) and len(data) > 0:
                        generated = data[0].get("generated_text", "")
                        st.write(generated)
                    else:
                        st.json(data)
                else:
                    st.error(f"❌ {result['error']}")
    
    with subtab3:
        st.subheader("Résumé de Texte")
        text = st.text_area("Texte à résumer", 
                           "The quick brown fox jumps over the lazy dog. This is a sample text for summarization.", 
                           height=150)
        model = "facebook/bart-large-cnn"
        
        if st.button("Résumer"):
            with st.spinner("Résumé..."):
                result = call_huggingface_api(model, text, hf_token)
                if result["success"]:
                    st.success("✅ Résumé:")
                    data = result["data"]
                    if isinstance(data, list) and len(data) > 0:
                        summary = data[0].get("summary_text", "")
                        st.write(summary)
                    else:
                        st.json(data)
                else:
                    st.error(f"❌ {result['error']}")

with tab2:
    st.header("🎨 Génération d'Images")
    
    st.warning("⚠️ **Limitation**: Les modèles de génération d'images ne sont généralement pas disponibles via l'API gratuite.")
    
    with st.expander("💡 Solutions alternatives"):
        st.write("**Options recommandées:**")
        st.write("1. 🔑 API payante Hugging Face Inference Endpoints")
        st.write("2. 💻 Installation locale avec diffusers")
        st.write("3. 🌐 Services externes (OpenAI DALL-E, Midjourney)")
        
        st.code("""
# Installation locale
pip install diffusers torch
        
# Code Python
from diffusers import StableDiffusionPipeline
pipe = StableDiffusionPipeline.from_pretrained("runwayml/stable-diffusion-v1-5")
image = pipe("A beautiful sunset").images[0]
        """)
    
    # Interface de test (même si limitée)
    prompt = st.text_area("Prompt d'image", "A beautiful sunset over mountains", height=100)
    model = st.selectbox("Modèle (disponibilité limitée)", [
        "runwayml/stable-diffusion-v1-5",
        "CompVis/stable-diffusion-v1-4",
        "dreamlike-art/dreamlike-diffusion-1.0"
    ])
    
    if st.button("🎨 Tenter la génération"):
        with st.spinner("Tentative de génération..."):
            result = call_huggingface_api(model, prompt, hf_token, "image")
            if result["success"]:
                try:
                    image = Image.open(io.BytesIO(result["data"]))
                    st.success("✅ Image générée!")
                    st.image(image, caption=prompt, use_column_width=True)
                    
                    st.download_button(
                        "💾 Télécharger",
                        result["data"],
                        f"generated_{hash(prompt) % 10000}.png",
                        "image/png"
                    )
                except Exception as e:
                    st.error(f"❌ Erreur d'affichage: {e}")
            else:
                st.error(f"❌ {result['error']}")
                if "non trouvé" in result['error'] or "404" in result['error']:
                    st.info("💡 Ce modèle n'est pas disponible via l'API gratuite.")

with tab3:
    st.header("❓ Question-Réponse")
    
    context = st.text_area("Contexte", 
                          "Paris is the capital of France. It is known for the Eiffel Tower.", 
                          height=100)
    question = st.text_input("Question", "What is the capital of France?")
    model = "distilbert-base-cased-distilled-squad"
    
    if st.button("Répondre"):
        if context and question:
            with st.spinner("Recherche..."):
                inputs = {"question": question, "context": context}
                result = call_huggingface_api(model, inputs, hf_token, "question-answering")
                if result["success"]:
                    st.success("✅ Réponse:")
                    data = result["data"]
                    answer = data.get("answer", "Pas de réponse")
                    score = data.get("score", 0)
                    st.write(f"**Réponse**: {answer}")
                    st.write(f"**Confiance**: {score:.4f}")
                else:
                    st.error(f"❌ {result['error']}")
        else:
            st.warning("Veuillez remplir le contexte et la question")

with tab4:
    st.header("📊 Informations")
    
    st.subheader("✅ Avantages de cette version")
    st.write("• Pas de conflit Keras/TensorFlow")
    st.write("• Utilise uniquement l'API d'inférence")
    st.write("• Plus léger et plus rapide")
    st.write("• Pas de téléchargement de modèles")
    
    st.subheader("🎯 Fonctionnalités opérationnelles")
    st.write("✅ Analyse de sentiment")
    st.write("✅ Génération de texte")
    st.write("✅ Résumé de texte")
    st.write("✅ Question-réponse")
    st.write("⚠️ Génération d'images (limitée)")
    
    st.subheader("🔧 Configuration système")
    if hf_token:
        st.write(f"🔑 Token: {hf_token[:10]}...")
    st.write(f"🐍 Python: Streamlit + Requests uniquement")
    st.write("📦 Pas de dépendances lourdes (PyTorch, TensorFlow)")
    
    st.subheader("💡 Conseils d'utilisation")
    st.write("• Cette version évite tous les conflits de dépendances")
    st.write("• Parfaite pour tester l'API Hugging Face")
    st.write("• Pour la génération d'images, utilisez l'installation locale")
    st.write("• Toutes les fonctionnalités de texte sont pleinement opérationnelles")

st.markdown("---")
st.markdown("🤗 Version sans Keras - Optimisée pour l'API d'inférence uniquement")
